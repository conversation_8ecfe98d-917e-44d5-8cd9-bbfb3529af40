import React, { useState } from 'react';
import { PromptItem } from './types';
import { Layout } from './components/Layout';
import { Gallery } from './components/Gallery';
import { AddEditForm } from './components/AddEditForm';
import { ItemDetailModal } from './components/ItemDetailModal';
import { FavoritesPage } from './components/FavoritesPage';
import { CategoriesPage } from './components/CategoriesPage';
import { SettingsPage } from './components/SettingsPage';
import { SidebarProvider } from './components/ui/sidebar';
import { Toaster } from './components/ui/sonner';
import { mockPrompts } from './data/mockData';

type AppMode = 'gallery' | 'add' | 'edit' | 'favorites' | 'categories' | 'settings';

export default function App() {
  const [items, setItems] = useState<PromptItem[]>(mockPrompts);
  const [mode, setMode] = useState<AppMode>('gallery');
  const [selectedItem, setSelectedItem] = useState<PromptItem | null>(null);
  const [editingItem, setEditingItem] = useState<PromptItem | null>(null);

  const addItem = (newItem: Omit<PromptItem, 'id' | 'dateAdded' | 'lastModified'>) => {
    const item: PromptItem = {
      ...newItem,
      id: Date.now().toString(),
      dateAdded: new Date(),
      lastModified: new Date(),
    };
    setItems(prev => [item, ...prev]);
    setMode('gallery');
  };

  const updateItem = (updatedItem: Omit<PromptItem, 'id' | 'dateAdded' | 'lastModified'>) => {
    if (!editingItem) return;
    
    setItems(prev => prev.map(item => 
      item.id === editingItem.id 
        ? { 
            ...item, 
            ...updatedItem,
            lastModified: new Date()
          }
        : item
    ));
    setEditingItem(null);
    setMode('gallery');
  };

  const deleteItem = (id: string) => {
    setItems(prev => prev.filter(item => item.id !== id));
    setSelectedItem(null);
  };

  const toggleFavorite = (id: string) => {
    setItems(prev => prev.map(item => 
      item.id === id ? { ...item, isFavorite: !item.isFavorite } : item
    ));
  };

  const handleEdit = (item: PromptItem) => {
    setEditingItem(item);
    setMode('edit');
  };

  const handleViewDetails = (item: PromptItem) => {
    setSelectedItem(item);
  };

  const handleAddNew = () => {
    setEditingItem(null);
    setMode('add');
  };

  const handleCancel = () => {
    setEditingItem(null);
    setMode('gallery');
  };

  const renderContent = () => {
    switch (mode) {
      case 'add':
      case 'edit':
        return (
          <AddEditForm
            item={editingItem}
            onSave={editingItem ? updateItem : addItem}
            onCancel={handleCancel}
          />
        );
      case 'favorites':
        return (
          <FavoritesPage
            items={items}
            onEdit={handleEdit}
            onDelete={deleteItem}
            onToggleFavorite={toggleFavorite}
            onViewDetails={handleViewDetails}
          />
        );
      case 'categories':
        return <CategoriesPage items={items} />;
      case 'settings':
        return <SettingsPage />;
      default:
        return (
          <Gallery
            items={items}
            onEdit={handleEdit}
            onDelete={deleteItem}
            onToggleFavorite={toggleFavorite}
            onViewDetails={handleViewDetails}
          />
        );
    }
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen w-full">
        <Layout 
          currentPage={mode}
          onNavigate={setMode}
          onAddNew={handleAddNew}
        >
          <div className="p-6">
            {renderContent()}
          </div>
        </Layout>
        
        {selectedItem && (
          <ItemDetailModal
            item={selectedItem}
            onClose={() => setSelectedItem(null)}
            onEdit={() => handleEdit(selectedItem)}
            onDelete={() => deleteItem(selectedItem.id)}
            onToggleFavorite={() => toggleFavorite(selectedItem.id)}
          />
        )}
        
        <Toaster position="top-right" />
      </div>
    </SidebarProvider>
  );
}